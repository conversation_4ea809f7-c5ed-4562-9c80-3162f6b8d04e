openapi: '3.0.0'

info:
    description: >
        Pentabell2.0 API provides endpoints for managing users, authentication, skills, clients, managers, candidates, job openings and more.
        It supports features such as user registration, sign-in, password management, and CRUD operations for various entities.
    version: '1.1.0'
    title: 'Pentabell2.0 API'
    contact:
        name: Pentabell2.0
        email: <EMAIL>
    lastUpdate: '2023-12-04'

servers:
    - url: 'http://localhost:4000/api/v1'
    - url: 'http://**************:4000/api/v1'
    - url: 'http://**************/api/v1'

components:
    securitySchemes:
        cookieAuth:
            type: apiKey
            in: cookie
            name: accessToken

    schemas:
        Error:
            type: object
            properties:
                status:
                    type: integer
                    example: 404
                message:
                    type: string
                    example: 'Category not found.'
        Category:
            type: object
            properties:
                _id:
                    type: string
                    description: The unique identifier for the category.
                    example: '6707ab921e6de2d54bc907e9'
                versions:
                    type: object
                    description: An object containing language-specific versions of the category.
                    properties:
                        en:
                            type: object
                            properties:
                                name:
                                    type: string
                                    example: 'Technology'
                                articles:
                                    type: array
                                    items:
                                        type: string
                                        example: '673208228603effaeaac6a9e'
                createdAt:
                    type: string
                    format: date-time
                updatedAt:
                    type: string
                    format: date-time
        CreateUserRequest:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                email:
                    type: string
                jobTitle:
                    type: string
                country:
                    type: string
                phone:
                    type: string
                role:
                    $ref: '#/components/schemas/Role'
        SignInRequest:
            type: object
            properties:
                email:
                    type: string
                password:
                    type: string
        ForgotPasswordRequest:
            type: object
            properties:
                email:
                    type: string
        ResetPasswordRequest:
            type: object
            properties:
                password:
                    type: string
        AssignRecruiterRequest:
            type: object
            properties:
                recruiters:
                    type: array
                    items:
                        type: string
        UpdateCandidateListRequest:
            type: object
            properties:
                from:
                    type: string
                to:
                    type: string
        User:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                email:
                    type: string
                jobTitle:
                    type: string
                country:
                    type: string
                phone:
                    type: string
                role:
                    $ref: '#/components/schemas/Role'
                profilePicture:
                    type: string
                password:
                    type: string
paths:
    /auth/signup:
        post:
            summary: Register a new user
            tags:
                - Auth
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SignUpRequest'
                        examples:
                            example1:
                                summary: Example of a candidate registration
                                value:
                                    firstName: candidate 0
                                    lastName: candidate 0
                                    email: <EMAIL>
                                    password: test123456
                                    phone: '+21628523641'
                                    country: Tunisia

            responses:
                '201':
                    description: User registered successfully
                '400':
                    description: Bad request
    /auth/signin:
        post:
            summary: Sign in as a user
            tags:
                - Auth
            requestBody:
                description: User sign-in data
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SignInRequest'
                        examples:
                            example1:
                                summary: Example of candidate login
                                value:
                                    email: <EMAIL>
                                    password: test123456

                            example2:
                                summary: Example of admin login
                                value:
                                    email: <EMAIL>
                                    password: admin123456
            responses:
                '200':
                    description: User signed in successfully
                '401':
                    description: Unauthorized
    /auth/logout:
        post:
            summary: Log out
            tags:
                - Auth
            responses:
                '200':
                    description: User logged out successfully
    /auth/forgot-password:
        post:
            summary: Request password reset
            tags:
                - Auth
            requestBody:
                description: User email for password reset
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ForgotPasswordRequest'
            responses:
                '200':
                    description: Password reset request successful
    /auth/reset-password/{token}:
        post:
            summary: Reset password
            tags:
                - Auth
            parameters:
                - name: token
                  in: path
                  required: true
                  description: Reset password token
                  schema:
                      type: string
                - name: body
                  in: body
                  description: New password
                  required: true
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ResetPasswordRequest'
            responses:
                '200':
                    description: Password reset successful
    /auth/confirm-account/{token}:
        post:
            summary: Confirm account
            tags:
                - Auth
            parameters:
                - name: token
                  in: path
                  required: true
                  description: Confirmation token
                  schema:
                      type: string
            responses:
                '200':
                    description: Account confirmation successful
    /users:
        get:
            summary: Get all users
            tags:
                - Users
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: role
                  schema:
                      type: string
                - in: query
                  name: status
                  schema:
                      type: string
                - in: query
                  name: firstName
                  schema:
                      type: string
                - in: query
                  name: jobTitle
                  schema:
                      type: string
                - in: query
                  name: country
                  schema:
                      type: string
                - in: query
                  name: isArchived
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
        post:
            summary: Create a new user
            tags:
                - Users
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateUserRequest'
                        examples:
                            example1:
                                summary: Example of creating a candidate
                                value:
                                    firstName: Candidate
                                    lastName: user
                                    email: <EMAIL>
                                    phone: +21628523641
                                    country: Tunisia
                                    roles:
                                        - Candidate
                            example4:
                                summary: Example of creating an admin
                                value:
                                    firstName: Admin
                                    lastName: User
                                    email: <EMAIL>
                                    password: admin123
                                    phone: +1234567890
                                    country: Tunisia
                                    roles:
                                        - Admin
                            example5:
                                summary: Example of creating a recruiter
                                value:
                                    firstName: Recruiter
                                    lastName: User
                                    email: <EMAIL>
                                    password: recruiter123
                                    phone: +1234567890
                                    country: Tunisia
                                    roles:
                                        - Recruiter
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '409':
                    description: Email must be unique
    /users/{id}:
        get:
            summary: Get user by ID
            tags:
                - Users
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: User not found
                '406':
                    description: Invalid Mongo ID
        put:
            summary: Update user
            tags:
                - Users
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateUserRequest'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: User not found
        delete:
            summary: Delete user
            tags:
                - Users
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: User not found
    /users/current:
        get:
            summary: Get current User
            tags:
                - Users
            security:
                - cookieAuth: []
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
    /account:
        get:
            summary: Get account
            tags:
                - Account
            security:
                - cookieAuth: []
            parameters:
                - in: cookie
                  name: accessToken
                  required: true
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: User not found
        put:
            summary: Update account
            tags:
                - Account
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        example: |
                            {
                              "firstName": "",
                              "lastName": "",
                              "phone": "",
                              "jobTitle": "",
                              "country": ""
                            }
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: User not found
    /account/password:
        put:
            summary: Update password
            tags:
                - Account
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        example: |
                            {
                              "currentPassword": "",
                              "newPassword": ""
                            }
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: User not found
    /categories:
        post:
            summary: Create a new category with versions
            tags:
                - Categories
            security:
                - bearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - versions
                            properties:
                                versions:
                                    type: object
                                    description: Map of language codes to category version objects
                                    additionalProperties:
                                        type: object
                                        required:
                                            - name
                                        properties:
                                            language:
                                                type: string
                                                example: en
                                            name:
                                                type: string
                                                example: Electronics
                                            url:
                                                type: string
                                                example: electronics
                                            description:
                                                type: string
                                                example: Category for electronic devices
                                            image:
                                                type: string
                                                nullable: true
                                                example: https://example.com/image.jpg
                                            canonical:
                                                type: string
                                                example: https://example.com/electronics
                                            articles:
                                                type: array
                                                items:
                                                    type: string
                                                    format: objectId
                                            guides:
                                                type: array
                                                items:
                                                    type: string
                                                    format: objectId
                                            metaTitle:
                                                type: string
                                                example: Best Electronics
                                            metaDescription:
                                                type: string
                                                example: Find the best electronics here.
                                robotsMeta:
                                    type: string
                                    enum: [index, noindex, follow, nofollow] # adjust as per your robotsMeta enum
                                    example: index

            responses:
                '200':
                    description: Category created successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    category:
                                        $ref: '#/components/schemas/Category'
                '409':
                    description: Category with this name already exists
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
                '500':
                    description: Internal server error
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
        put:
            summary: Import a JSON file to convert to the new category model
            tags:
                - Categories
            consumes:
                - multipart/form-data
            requestBody:
                required: true
                content:
                    multipart/form-data:
                        schema:
                            type: object
                            properties:
                                file:
                                    type: string
                                    format: binary
                                    description: JSON file to upload
            responses:
                201:
                    description: Categories converted successfully
                400:
                    description: No file uploaded
                500:
                    description: Conversion failed
        get:
            tags:
                - Categories
            summary: Retrieve all categories
            description: |
                Fetches a paginated and filterable list of all categories. 
                You can use query parameters for pagination, sorting, and searching.
            parameters:
                - in: query
                  name: page
                  schema:
                      type: integer
                      default: 1
                  description: The page number for pagination.
                - in: query
                  name: limit
                  schema:
                      type: integer
                      default: 10
                  description: The number of categories to return per page.
                - in: query
                  name: sort
                  schema:
                      type: string
                  description: "Sort order. Use field name for ascending (e.g., 'createdAt') or prefix with '-' for descending (e.g., '-createdAt')."
                - in: query
                  name: search
                  schema:
                      type: string
                  description: A search term to filter categories by name or other text fields.
            responses:
                '200':
                    description: A list of categories was successfully retrieved.
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Category'
                '500':
                    description: Internal Server Error.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
    /categories/{categoryId}:
        get:
            tags:
                - Categories
            summary: Retrieve a single category by ID
            description: Fetches the details of a specific category using its unique identifier.
            parameters:
                - name: categoryId
                  in: path
                  required: true
                  description: The unique identifier of the category to retrieve.
                  schema:
                      type: string
                      example: '6707ab921e6de2d54bc907e9'
            responses:
                '200':
                    description: Category was successfully retrieved.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Category'
                '404':
                    description: Category not found.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
                '500':
                    description: Internal Server Error.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
    /categories/{language}/blog/{url}:
        get:
            tags:
                - Categories
            summary: Retrieve a category by language and URL slug
            description: Fetches a single category version based on its URL-friendly slug and language code.
            security:
                - ApiKeyAuth: []
            parameters:
                - name: language
                  in: path
                  required: true
                  description: "The two-letter language code of the category version (e.g., 'en', 'fr')."
                  schema:
                      type: string
                      example: 'en'
                - name: url
                  in: path
                  required: true
                  description: The URL-friendly slug of the category.
                  schema:
                      type: string
                      example: 'how-to-build-a-pc'
            responses:
                '200':
                    description: Category was successfully retrieved.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Category'
                '401':
                    description: Unauthorized. The API key is missing or invalid.
                '404':
                    description: No category was found for the specified language and slug.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
                '500':
                    description: Internal Server Error.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
                                components:
                                    schemas:
                                        Category:
                                            type: object
                                            properties:
                                                _id:
                                                    type: string
                                                    description: The unique identifier for the category.
                                                    example: '6707ab921e6de2d54bc907e9'
                                                versions:
                                                    type: object
                                                    description: An object containing language-specific versions of the category.
                                                    properties:
                                                        en:
                                                            type: object
                                                            properties:
                                                                name:
                                                                    type: string
                                                                    example: 'PC Building'
                                                                url:
                                                                    type: string
                                                                    example: 'how-to-build-a-pc'
                                                                articles:
                                                                    type: array
                                                                    items:
                                                                        type: string
                                                                        example: '673208228603effaeaac6a9e'
                                                createdAt:
                                                    type: string
                                                    format: date-time
                                                updatedAt:
                                                    type: string
                                                    format: date-time

                                        Error:
                                            type: object
                                            properties:
                                                status:
                                                    type: integer
                                                    example: 404
                                                message:
                                                    type: string
                                                    example: 'Category not found.'
    /categories/{language}/category/{url}:
            get:
                tags:
                    - Categories
                summary: Get all articles for a category by its slug
                description: |
                    Fetches a list of articles belonging to a specific category.
                    The category is identified by its language code and URL-friendly slug.
                    This endpoint requires both a valid API Key and user authentication.
                security:
                    - ApiKeyAuth: []
                    - BearerAuth: []
                parameters:
                    - name: language
                    in: path
                        required: true
                        description: "The two-letter language code of the category version (e.g., 'en', 'fr')."
                    schema:
                        type: string
                        example: 'en'
                    - name: url
                    in: path
                        required: true
                        description: The URL-friendly slug of the category whose articles are to be fetched.
                        schema:
                            type: string
                            example: 'latest-tech-news'
                responses:
                    '200':
                    description: A list of articles was successfully retrieved for the category.
                    content:
                        application/json:
                        schema:
                            type: array
                            items:
                                $ref: '#/components/schemas/Article'
                    '401':
                        description: Unauthorized. The API key or user authentication token is missing or invalid.
                    '404':
                        description: No category was found for the specified language and slug.
                    content:
                        application/json:
                        schema:
                            $ref: '#/components/schemas/Error'
                    '500':
                        description: Internal Server Error.
                    content:
                        application/json:
                        schema:
                            $ref: '#/components/schemas/Error'



